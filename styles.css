/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    overflow: hidden;
    height: 100vh;
    margin: 0;
    padding: 0;
}

/* Presentation Container */
.presentation-container {
    position: relative;
    width: 100%;
    height: 100vh;
}

/* Slide Styles */
.slide {
    display: none;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
    padding: 40px 60px;
    position: relative;
    animation: slideIn 0.6s ease-out;
    overflow: hidden;
    box-sizing: border-box;
}

.slide.active {
    display: block;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Add subtle floating animation for logos */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.logos-container img {
    animation: float 6s ease-in-out infinite;
}

.logos-container img:nth-child(2) {
    animation-delay: 3s;
}

/* Title Slide */
.title-slide {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.logos-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.spark-logo {
    height: 180px;
    width: auto;
    object-fit: contain;
}

.title-slide h1 {
    font-size: 4.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-slide h2 {
    font-size: 2.5rem;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 40px;
}

.subtitle p {
    font-size: 1.4rem;
    color: #718096;
    margin-bottom: 50px;
    max-width: 800px;
    line-height: 1.6;
}

.location-info {
    display: flex;
    gap: 40px;
    flex-wrap: wrap;
    justify-content: center;
}

.location-info p {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1rem;
    color: #4a5568;
    background: #f7fafc;
    padding: 15px 25px;
    border-radius: 50px;
    border: 2px solid #e2e8f0;
}

.location-info i {
    color: #667eea;
}

/* Content Slides */
.slide-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.slide-content h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    flex-shrink: 0;
}

.slide-content h1 i {
    color: #667eea;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    flex: 1;
    overflow: visible;
    min-height: 0;
    align-items: start;
}

.highlight-box {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 30px;
    border-radius: 15px;
    grid-column: 1 / -1;
}

.highlight-box h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.highlight-box p {
    font-size: 1.3rem;
    line-height: 1.6;
    opacity: 0.95;
}

/* Stats Section */
.stats-section {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #f7fafc;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.stat-number {
    font-size: 5rem;
    font-weight: 700;
    color: #667eea;
}

.stat-label {
    font-size: 1.3rem;
    color: #2d3748;
    font-weight: 500;
}

.stat-source {
    font-size: 0.9rem;
    color: #718096;
    font-style: italic;
}

/* Key Points */
.key-points {
    background: #f7fafc;
    padding: 30px;
    border-radius: 15px;
}

.key-points h3 {
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
}

.key-points ul {
    list-style: none;
}

.key-points li {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 1rem;
    color: #4a5568;
    line-height: 1.5;
}

.key-points i {
    color: #667eea;
    font-size: 0.8rem;
}

/* Mandate Grid */
.mandate-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    flex: 1;
    overflow: hidden;
    min-height: 0;
}

.mandate-card {
    padding: 50px 40px;
    border-radius: 20px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.mandate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.mandate-card.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.mandate-card.secondary {
    background: #f7fafc;
    border: 2px solid #e2e8f0;
}

.mandate-card.tertiary {
    background: #f0fff4;
    border: 2px solid #9ae6b4;
}

.mandate-card i {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.mandate-card.primary i {
    color: white;
}

.mandate-card.secondary i {
    color: #667eea;
}

.mandate-card.tertiary i {
    color: #38a169;
}

.mandate-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.mandate-card p {
    font-size: 1rem;
    line-height: 1.6;
    opacity: 0.9;
}

/* Navigation - Hidden */
.navigation {
    display: none;
}

.nav-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.nav-btn:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.slide-counter {
    font-weight: 600;
    color: #2d3748;
    font-size: 1rem;
}

/* Slide Indicators - Hidden */
.slide-indicators {
    display: none;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: #667eea;
    transform: scale(1.2);
}

.indicator:hover {
    background: #667eea;
    opacity: 0.8;
}

/* Description Grid */
.description-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    flex: 1;
    overflow: hidden;
    min-height: 0;
}

.description-card {
    background: #f7fafc;
    padding: 25px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
    transition: transform 0.3s ease;
}

.description-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.description-card.main {
    grid-column: 1 / -1;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.description-card i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 15px;
    display: block;
}

.description-card.main i {
    color: white;
}

.description-card h3, .description-card h4 {
    margin-bottom: 10px;
    font-weight: 600;
}

.description-card p {
    line-height: 1.6;
    opacity: 0.9;
}

/* Objectives */
.objectives-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
    overflow: visible;
    min-height: 0;
}

.objective-item {
    display: flex;
    align-items: flex-start;
    gap: 25px;
    padding: 30px;
    background: white;
    border-radius: 16px;
    border-left: 5px solid #667eea;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #f1f5f9;
}

.objective-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 70px;
    height: 70px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    font-weight: 700;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.objective-content h3 {
    font-size: 1.4rem;
    color: #1f2937;
    margin-bottom: 12px;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.objective-content p {
    color: #6b7280;
    line-height: 1.7;
    font-size: 0.95rem;
}

/* Outputs Grid */
.outputs-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    flex: 1;
    overflow: hidden;
    min-height: 0;
}

.output-card {
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.output-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.output-card.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.output-card.secondary {
    background: #f0fff4;
    border: 2px solid #9ae6b4;
}

.output-card.tertiary {
    background: #fffaf0;
    border: 2px solid #fbd38d;
}

.output-card.quaternary {
    background: #f7fafc;
    border: 2px solid #cbd5e0;
}

.output-card i {
    font-size: 2.5rem;
    margin-bottom: 20px;
    display: block;
}

.output-card.primary i {
    color: white;
}

.output-card.secondary i {
    color: #38a169;
}

.output-card.tertiary i {
    color: #dd6b20;
}

.output-card.quaternary i {
    color: #4a5568;
}

.output-card h3 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.output-card p {
    line-height: 1.6;
    opacity: 0.9;
}

/* Schedule Container */
.schedule-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    flex: 1;
    overflow: visible;
    min-height: 0;
    align-items: start;
}

.schedule-column {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.schedule-item {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 15px;
    background: #f7fafc;
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.schedule-item:hover {
    transform: translateX(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.schedule-item.highlight {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.day-badge {
    background: #667eea;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    font-weight: 600;
    min-width: 100px;
    text-align: center;
}

.schedule-item.highlight .day-badge {
    background: rgba(255, 255, 255, 0.2);
}

.schedule-details h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.schedule-details p {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
    opacity: 0.8;
}

/* Methodology Grid */
.methodology-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    flex: 1;
    overflow: visible;
    min-height: 0;
    align-items: stretch;
}

.method-card {
    padding: 30px 25px;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.method-card.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
}

.method-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
    display: block;
}

.method-card.primary i {
    color: white;
}

.method-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
    color: #1f2937;
}

.method-card.primary h3 {
    color: white;
}

.method-card p {
    line-height: 1.6;
    opacity: 0.8;
    font-size: 0.95rem;
    flex-grow: 1;
}

.method-card.primary p {
    opacity: 0.95;
}

/* Prerequisites */
.prerequisites-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.prereq-section {
    background: #f7fafc;
    padding: 25px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
}

.prereq-section h3 {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
    color: #2d3748;
    margin-bottom: 20px;
    font-weight: 600;
}

.prereq-section i {
    color: #667eea;
}

.prereq-list {
    list-style: none;
}

.prereq-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    color: #4a5568;
    line-height: 1.5;
}

.prereq-list i {
    color: #38a169;
    font-size: 0.9rem;
}

/* Modules */
.modules-container {
    flex: 1;
    overflow: visible;
    min-height: 0;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 12px;
}

.module-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.module-item:hover {
    background: #667eea;
    color: white;
    transform: translateX(10px);
}

.module-number {
    background: #667eea;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.module-item:hover .module-number {
    background: white;
    color: #667eea;
}

.module-title {
    font-weight: 500;
    font-size: 1rem;
}

/* Partnership Container */
.partnership-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    flex: 1;
    overflow: hidden;
    min-height: 0;
}

.investment-summary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
}

.investment-summary h3 {
    font-size: 1.5rem;
    margin-bottom: 25px;
    font-weight: 600;
}

.investment-breakdown {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.investment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.investment-label {
    font-weight: 500;
}

.investment-amount {
    font-size: 1.2rem;
    font-weight: 700;
}

.partnership-benefits h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 25px;
    font-weight: 600;
}

.benefits-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.benefit-item {
    background: #f7fafc;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    text-align: center;
    transition: transform 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.benefit-item i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
    display: block;
}

.benefit-item h4 {
    font-size: 1rem;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
}

.benefit-item p {
    font-size: 0.9rem;
    color: #4a5568;
    line-height: 1.4;
}

/* Call to Action */
.cta-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    text-align: center;
}

.cta-header {
    margin-bottom: 40px;
}

.cta-header i {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 20px;
}

.cta-header h1 {
    font-size: 2.5rem;
    color: #2d3748;
    margin-bottom: 15px;
    font-weight: 700;
}

.cta-header p {
    font-size: 1.2rem;
    color: #718096;
}

.cta-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    width: 100%;
    margin-bottom: 40px;
}

.cta-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.cta-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cta-stat .stat-number {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
}

.cta-stat .stat-label {
    font-size: 0.9rem;
    color: #4a5568;
    text-align: center;
}

.cta-action {
    background: #f7fafc;
    padding: 30px;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
}

.cta-action h3 {
    font-size: 1.5rem;
    color: #2d3748;
    margin-bottom: 15px;
    font-weight: 600;
}

.cta-action p {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 20px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.contact-info p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #667eea;
    font-weight: 500;
}

.cta-footer {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px 40px;
    border-radius: 50px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slide {
        width: 100vw;
        height: 100vh;
        padding: 30px 25px;
    }

    .title-slide h1 {
        font-size: 3rem;
    }

    .title-slide h2 {
        font-size: 1.8rem;
    }

    .slide-content h1 {
        font-size: 2.2rem;
        margin-bottom: 30px;
    }

    .content-grid, .description-grid, .outputs-grid, .partnership-container, .cta-content, .schedule-container, .methodology-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .mandate-grid, .methodology-grid, .prerequisites-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .modules-grid {
        grid-template-columns: 1fr;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
    }

    .cta-stats {
        flex-direction: column;
        gap: 20px;
    }

    .location-info {
        flex-direction: column;
        gap: 15px;
    }

    .logos-container {
        gap: 20px;
    }

    .spark-logo {
        height: 120px;
    }

    .objectives-container {
        gap: 20px;
    }

    .objective-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .schedule-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
}
